import { List, Popover, Spin } from "antd";
import { useState, useEffect } from "react";
import { Api } from "./Api";
import dayjs from "dayjs";
import UserManager from "@/components/UserManager";

interface JoinListProps {
  children: React.ReactElement;
  groupId: string | number;
  joinNum: number;
}

interface JoinMember {
  id: string;
  name: string;
  joinTime: string;
  userId: string;
}

export const JoinList = ({ children, groupId, joinNum }: JoinListProps) => {
  const [loading, setLoading] = useState(false);
  const [joinList, setJoinList] = useState<JoinMember[]>([]);
  const [visible, setVisible] = useState(false);

  const fetchJoinList = async () => {
    if (!groupId || joinNum === 0) return;
    
    setLoading(true);
    try {
      const res = await Api.getJoinList({ params: { joinerId: groupId } });
      setJoinList(res?.joinList || []);
    } catch (error) {
      console.error("获取参团人列表失败:", error);
      setJoinList([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (visible) {
      fetchJoinList();
    }
  }, [visible, groupId]);

  const content = (
    <div style={{ width: 280, maxHeight: 300, overflow: "auto" }}>
      <Spin spinning={loading}>
        {joinList.length === 0 && !loading ? (
          <div style={{ padding: "16px", textAlign: "center", color: "#999" }}>
            暂无参团人
          </div>
        ) : (
          <List
            size="small"
            dataSource={joinList}
            renderItem={(item) => (
              <List.Item style={{ padding: "8px 12px", borderBottom: "1px solid #f0f0f0" }}>
                <div style={{ width: "100%" }}>
                  <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
                    <UserManager userId={item.userId}>
                      <span style={{ fontWeight: 500, color: "#333" }}>{item.name}</span>
                    </UserManager>
                    <span style={{ fontSize: "12px", color: "#999" }}>
                      {dayjs(item.joinTime).format("MM-DD HH:mm")}
                    </span>
                  </div>
                </div>
              </List.Item>
            )}
          />
        )}
      </Spin>
    </div>
  );

  // 如果参团人数为0，不显示悬浮效果
  if (joinNum === 0) {
    return children;
  }

  return (
    <Popover
      content={content}
      title={`参团人列表 (${joinNum}人)`}
      trigger="hover"
      placement="topLeft"
      open={visible}
      onOpenChange={setVisible}
      arrow={false}
      overlayStyle={{ maxWidth: 300 }}
    >
      <span style={{ cursor: "pointer", color: "#1890ff" }}>
        {children}
      </span>
    </Popover>
  );
};
