import { makeApi } from "@/utils/Api";

export const Api = {
  getMarketProduct: makeApi("get", `/api/mallMarketProduct`),
  getMarketProductInfo: makeApi("get", `/api/mallMarketProduct`, true),
  addMarketProduct: makeApi("post", `/api/mallMarketProduct`),
  putMarketProduct: makeApi("put", `/api/mallMarketProduct`),
  delMarketProduct: makeApi("delete", `/api/mallMarketProduct`),
  batchModifySaleIn: makeApi("post", `/api/mallMarketProduct/batch_modify_sale_in`),
  operateSold: makeApi("post", `/api/mallMarketProduct/operate_sold_count`),
  operateInventory: makeApi("post", `/api/mallMarketProduct/operate_inventory_count`),

  getRule: makeApi("get", `/api/mallMarketProduct/query_rule`),
  putRule: makeApi("post", `/api/mallMarketProduct/save_rule`),

  getLogs: makeApi("get", `/api/mallGroup`),
  virtualOpenGroup: makeApi("post", `/api/mallGroup/virtual_open_group`),
  virtualJoinGroup: makeApi("post", `/api/mallGroup/virtual_join_group`),

  // 获取参团人列表
  getJoinList: makeApi("get", `/api/mallGroup/join_list`),
};
